import * as bootstrap from 'bootstrap'

export const useBootstrap = () => {
  const initializeDropdowns = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem dropdown komponente...')

        // Uni<PERSON>ti postojeće dropdown instance da <PERSON><PERSON><PERSON><PERSON> duplikate
        const existingDropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        console.log(`Pronašao ${existingDropdowns.length} postojećih dropdown elemenata`)
        existingDropdowns.forEach(element => {
          const existingInstance = bootstrap.Dropdown.getInstance(element)
          if (existingInstance) {
            existingInstance.dispose()
          }
        })

        // Kreiraj nove dropdown instance
        const dropdownElementList = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        console.log(`Kreiram ${dropdownElementList.length} novih dropdown instanci`)

        const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
          console.log('Kreiram dropdown za element:', dropdownToggleEl)

          // Dodaj eksplicitni event listener za hover i click
          dropdownToggleEl.addEventListener('mouseenter', function() {
            // Hover efekat za desktop
            if (window.innerWidth >= 992) {
              const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this)
              dropdown.show()
            }
          })

          dropdownToggleEl.addEventListener('mouseleave', function() {
            // Sakrij dropdown kada miš napusti element (samo za desktop)
            if (window.innerWidth >= 992) {
              setTimeout(() => {
                const dropdown = bootstrap.Dropdown.getInstance(this)
                if (dropdown) {
                  dropdown.hide()
                }
              }, 100)
            }
          })

          return new bootstrap.Dropdown(dropdownToggleEl, {
            // Opcije za dropdown
            autoClose: true,
            boundary: 'clippingParents'
          })
        })

        console.log(`Inicijalizovano ${dropdownList.length} dropdown komponenti`)
        return dropdownList
      })
    }
  }

  const initializeCollapses = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem collapse komponente...')

        // Uništi postojeće collapse instance
        const existingCollapses = document.querySelectorAll('[data-bs-toggle="collapse"]')
        console.log(`Pronašao ${existingCollapses.length} postojećih collapse elemenata`)
        existingCollapses.forEach(element => {
          const existingInstance = bootstrap.Collapse.getInstance(element)
          if (existingInstance) {
            existingInstance.dispose()
          }
        })

        // Kreiraj nove collapse instance
        const collapseElementList = document.querySelectorAll('[data-bs-toggle="collapse"]')
        const collapseList = [...collapseElementList].map(collapseToggleEl => {
          return new bootstrap.Collapse(collapseToggleEl, {
            toggle: false
          })
        })

        console.log(`Inicijalizovano ${collapseList.length} collapse komponenti`)
        return collapseList
      })
    }
  }

  const initializeModals = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem modal komponente...')

        // Uništi postojeće modal instance
        const existingModals = document.querySelectorAll('[data-bs-toggle="modal"]')
        console.log(`Pronašao ${existingModals.length} postojećih modal elemenata`)
        existingModals.forEach(element => {
          const targetSelector = element.getAttribute('data-bs-target')
          if (targetSelector) {
            const targetElement = document.querySelector(targetSelector)
            if (targetElement) {
              const existingInstance = bootstrap.Modal.getInstance(targetElement)
              if (existingInstance) {
                existingInstance.dispose()
              }
            }
          }
        })

        // Kreiraj nove modal instance
        const modalElementList = document.querySelectorAll('.modal')
        const modalList = [...modalElementList].map(modalEl => {
          return new bootstrap.Modal(modalEl)
        })

        console.log(`Inicijalizovano ${modalList.length} modal komponenti`)
        return modalList
      })
    }
  }

  const initializeSideCollapse = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem side-collapse funkcionalnost...')

        // Side-collapse funkcionalnost kao u ao.css
        const sideslider = document.querySelector('[data-toggle=collapse-side]')
        if (sideslider) {
          const sel = sideslider.getAttribute('data-target')
          const sel2 = sideslider.getAttribute('data-target-2')

          if (!sel || !sel2) {
            console.log('data-target ili data-target-2 atributi nisu pronađeni')
            return
          }

          // Ukloni postojeće event listenere da izbegneš duplikate
          const existingHandler = (sideslider as any)._sideCollapseHandler
          if (existingHandler) {
            sideslider.removeEventListener('click', existingHandler)
          }

          // Kreiraj novi handler
          const clickHandler = function() {
            console.log('Side-collapse dugme kliknuto')
            const target = document.querySelector(sel)
            const target2 = document.querySelector(sel2)

            console.log('Target elements:', { target, target2, sel, sel2 })

            if (target && target2) {
              if (target.classList.contains('in')) {
                target.classList.remove('in')
                target2.classList.add('out')
                console.log('Zatvaranje menija')
              } else {
                target.classList.add('in')
                target2.classList.remove('out')
                console.log('Otvaranje menija')

                // Reinicijalizuj dropdown komponente nakon otvaranja side-collapse menija
                setTimeout(() => {
                  initializeMobileDropdowns()
                }, 100)
              }
            } else {
              console.log('Target elementi nisu pronađeni')
            }
          }

          // Dodeli handler i sačuvaj referencu
          sideslider.addEventListener('click', clickHandler)
          ;(sideslider as any)._sideCollapseHandler = clickHandler

          console.log('Side-collapse inicijalizovan')
        } else {
          console.log('Side-collapse dugme nije pronađeno')
        }
      })
    }
  }

  const initializeMobileDropdowns = () => {
    if (process.client) {
      console.log('Inicijalizujem mobile dropdown komponente...')

      // Proverava da li je trenutno mobilni prikaz (ispod 992px)
      const isMobile = window.innerWidth < 992
      console.log(`Trenutna širina ekrana: ${window.innerWidth}px, isMobile: ${isMobile}`)

      if (!isMobile) {
        console.log('Nije mobilni prikaz, preskačem inicijalizaciju mobilnih dropdown-a')
        return
      }

      // Pronađi sve dropdown elemente u side-collapse meniju
      const sideCollapseDropdowns = document.querySelectorAll('.side-collapse [data-bs-toggle="dropdown"]')
      console.log(`Pronašao ${sideCollapseDropdowns.length} dropdown elemenata u side-collapse meniju`)

      sideCollapseDropdowns.forEach(dropdownToggleEl => {
        // Uništi postojeću instancu ako postoji
        const existingInstance = bootstrap.Dropdown.getInstance(dropdownToggleEl)
        if (existingInstance) {
          existingInstance.dispose()
        }

        // Ukloni postojeće event listenere
        const existingHandler = (dropdownToggleEl as any)._mobileDropdownHandler
        if (existingHandler) {
          dropdownToggleEl.removeEventListener('click', existingHandler)
        }

        console.log('Mobile dropdown kreiran za element:', dropdownToggleEl)

        // Kreiraj novi event handler
        const clickHandler = function(e: Event) {
          e.preventDefault()
          e.stopPropagation()

          console.log('Mobile dropdown klik event')

          // Pronađi dropdown menu
          const dropdownMenu = (this as Element).nextElementSibling
          if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
            // Toggle show klasu
            if (dropdownMenu.classList.contains('show')) {
              dropdownMenu.classList.remove('show')
              console.log('Zatvaranje mobile dropdown-a')
            } else {
              // Zatvori sve ostale dropdown menuje
              document.querySelectorAll('.side-collapse .dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show')
              })

              dropdownMenu.classList.add('show')
              console.log('Otvaranje mobile dropdown-a')
            }
          }
        }

        // Dodaj event listener i sačuvaj referencu
        dropdownToggleEl.addEventListener('click', clickHandler)
        ;(dropdownToggleEl as any)._mobileDropdownHandler = clickHandler
      })
    }
  }

  const initializeAllComponents = () => {
    console.log('Inicijalizujem sve Bootstrap komponente...')
    initializeDropdowns()
    initializeCollapses()
    initializeModals()
    initializeSideCollapse()
    // Dodaj inicijalizaciju mobilnih dropdown-a sa kašnjenjem
    setTimeout(() => {
      initializeMobileDropdowns()
    }, 200)
  }

  const getDropdownInstance = (element: Element) => {
    return bootstrap.Dropdown.getInstance(element)
  }

  const getCollapseInstance = (element: Element) => {
    return bootstrap.Collapse.getInstance(element)
  }

  const getModalInstance = (element: Element) => {
    return bootstrap.Modal.getInstance(element)
  }

  return {
    bootstrap,
    initializeDropdowns,
    initializeCollapses,
    initializeModals,
    initializeAllComponents,
    getDropdownInstance,
    getCollapseInstance,
    getModalInstance
  }
}
